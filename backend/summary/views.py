from rest_framework.viewsets import ModelViewSet
from rest_framework.views import APIView
from datetime import datetime
from collections import defaultdict, OrderedDict
from django.db.models import Decimal<PERSON>ield, Sum, Value, Q
from django.db.models.functions import Coalesce
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from rest_framework.decorators import action
from rest_framework import status
import traceback
from accounting.models import ChartofAccount, JournalEntryTransaction, Clinic
from utils.response_template import custom_error_response, custom_success_response

# Cache timeout in seconds (24 hours)
CACHE_TTL = 60 * 60 * 24


class ProfitAndLossViewSet(ModelViewSet):
    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="revenue/chart")
    def revenue_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            chart_of_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values_list("id", flat=True)

            yearly_data = []
            revenue_by_year = {}

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                journal_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=chart_of_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                revenue_data = journal_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                revenue_by_year[y] = (
                    revenue_data["total_credit"] - revenue_data["total_debit"]
                )

            for y in range(start_year, end_year + 1):
                revenue = revenue_by_year[y]
                prev_revenue = revenue_by_year.get(y - 1)

                if prev_revenue and prev_revenue != 0:
                    percentage = float((revenue - prev_revenue) / prev_revenue * 100)
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(revenue, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="ebitda/chart")
    def ebitda_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Revenue accounts
            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            expense_accounts = (
                ChartofAccount.objects.filter(
                    account_type="Expenses",
                    entity__currency=currency,
                )
                .exclude(
                    Q(account_name__icontains="interest")
                    | Q(account_name__icontains="tax")
                    | Q(account_name__icontains="depreciation")
                    | Q(account_name__icontains="amortization")
                )
                .values_list("id", flat=True)
            )

            ebitda_by_year = {}
            yearly_data = []

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                revenue_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=revenue_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                revenue_data = revenue_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                total_revenue = (
                    revenue_data["total_credit"] - revenue_data["total_debit"]
                )

                expense_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=expense_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                expense_data = expense_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                total_expense = (
                    expense_data["total_debit"] - expense_data["total_credit"]
                )

                ebitda_by_year[y] = total_revenue - total_expense

            for y in range(start_year, end_year + 1):
                ebitda = ebitda_by_year[y]
                prev_ebitda = ebitda_by_year.get(y - 1)

                if prev_ebitda and prev_ebitda != 0:
                    percentage = float((ebitda - prev_ebitda) / prev_ebitda * 100)
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(ebitda, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="net-profit/chart")
    def net_profit_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            expense_accounts = ChartofAccount.objects.filter(
                account_type__in=["Expenses"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            net_profit_by_year = {}
            yearly_data = []

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                revenue_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=revenue_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                revenue_data = revenue_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                total_revenue = (
                    revenue_data["total_credit"] - revenue_data["total_debit"]
                )

                expense_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=expense_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                expense_data = expense_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                total_expense = (
                    expense_data["total_debit"] - expense_data["total_credit"]
                )

                net_profit_by_year[y] = total_revenue - total_expense

            for y in range(start_year, end_year + 1):
                net_profit = net_profit_by_year[y]
                prev_net_profit = net_profit_by_year.get(y - 1)

                if prev_net_profit and prev_net_profit != 0:
                    percentage = float(
                        (net_profit - prev_net_profit) / prev_net_profit * 100
                    )
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(net_profit, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(
        detail=False, methods=["get"], url_path="revenue-breakdown/by-segment/chart"
    )
    def revenue_breakdown_by_segment_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            today = datetime.now()
            month_day = (today.month, today.day)

            chart_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values("id", "clinic_code")

            coa_id_to_clinic_code = {
                str(c["id"]): c["clinic_code"] for c in chart_accounts
            }
            coa_ids = list(coa_id_to_clinic_code.keys())

            clinic_code_to_segment = dict(Clinic.objects.values_list("code", "segment"))

            year_data = {}
            all_segments = set()

            for y in range(start_year, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, month_day[0], month_day[1])

                journal_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=coa_ids,
                    transaction_date__range=[start_date, end_date],
                ).select_related("chart_of_account")

                revenue_by_segment = defaultdict(float)

                for entry in journal_entries:
                    coa_id = str(entry.chart_of_account_id)
                    clinic_code = coa_id_to_clinic_code.get(coa_id)
                    segment = clinic_code_to_segment.get(clinic_code, "Others")

                    credit = float(entry.reporting_credit_amount or 0)
                    debit = float(entry.reporting_debit_amount or 0)
                    amount = credit - debit
                    revenue_by_segment[segment] += amount

                all_segments.update(revenue_by_segment.keys())

                sorted_segments = sorted(
                    [s for s in revenue_by_segment if s != "Others"]
                )
                if "Others" in revenue_by_segment:
                    sorted_segments.append("Others")

                total = sum(revenue_by_segment.values())

                record = {
                    "name": f"YTD<br/>{y}",
                    "total": round(total, 2),
                }

                for seg in sorted_segments:
                    record[seg] = round(revenue_by_segment.get(seg, 0.0), 2)

                year_data[y] = record

            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            formatted_data = []
            for y in range(start_year, end_year + 1):
                record = year_data[y]
                for seg in final_segments:
                    if seg not in record:
                        record[seg] = 0.0

                ordered_record = OrderedDict(
                    [("name", record["name"]), ("total", record["total"])]
                    + [(seg, record[seg]) for seg in final_segments]
                )
                formatted_data.append(ordered_record)

            return custom_success_response(formatted_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


class BusinessOverviewView(APIView):
    def get(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))

            # Helper function to get current year data for metrics
            def get_current_year_data(account_types, is_revenue=True):
                start_date = datetime(year, 1, 1)
                end_date = datetime(year, 12, 31)

                chart_of_accounts = ChartofAccount.objects.filter(
                    account_type__in=account_types, entity__currency=currency
                ).values_list("id", flat=True)

                journal_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=chart_of_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                data = journal_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                if is_revenue:
                    amount = data["total_credit"] - data["total_debit"]
                else:
                    amount = data["total_debit"] - data["total_credit"]

                return float(amount)

            # Helper function to get previous year data for comparison
            def get_previous_year_data(account_types, is_revenue=True):
                prev_year = year - 1
                start_date = datetime(prev_year, 1, 1)
                end_date = datetime(prev_year, 12, 31)

                chart_of_accounts = ChartofAccount.objects.filter(
                    account_type__in=account_types, entity__currency=currency
                ).values_list("id", flat=True)

                journal_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=chart_of_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                data = journal_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                if is_revenue:
                    amount = data["total_credit"] - data["total_debit"]
                else:
                    amount = data["total_debit"] - data["total_credit"]

                return float(amount)

            # Get Gross Revenue data
            current_revenue = get_current_year_data(
                ["Revenues", "Income"], is_revenue=True
            )
            prev_revenue = get_previous_year_data(
                ["Revenues", "Income"], is_revenue=True
            )
            revenue_change = (
                ((current_revenue - prev_revenue) / prev_revenue * 100)
                if prev_revenue != 0
                else 0
            )

            # Get EBITDA data (Revenue - Operating Expenses)
            current_expenses = get_current_year_data(
                ["Operating Expenses", "Expenses"], is_revenue=False
            )
            prev_expenses = get_previous_year_data(
                ["Operating Expenses", "Expenses"], is_revenue=False
            )
            current_ebitda = current_revenue - current_expenses
            prev_ebitda = prev_revenue - prev_expenses
            ebitda_change = (
                ((current_ebitda - prev_ebitda) / prev_ebitda * 100)
                if prev_ebitda != 0
                else 0
            )

            # Get Bank Debt data
            current_debt = get_current_year_data(
                ["Long-term Debt", "Bank Loans", "Liabilities"], is_revenue=False
            )
            prev_debt = get_previous_year_data(
                ["Long-term Debt", "Bank Loans", "Liabilities"], is_revenue=False
            )
            debt_change = (
                ((current_debt - prev_debt) / prev_debt * 100) if prev_debt != 0 else 0
            )

            # Get Cash Balance data
            current_cash = get_current_year_data(
                ["Cash", "Cash and Cash Equivalents"], is_revenue=True
            )
            prev_cash = get_previous_year_data(
                ["Cash", "Cash and Cash Equivalents"], is_revenue=True
            )
            cash_change = (
                ((current_cash - prev_cash) / prev_cash * 100) if prev_cash != 0 else 0
            )

            # Mock FTE data (would need HR system integration)
            current_fte = 150  # Mock current year FTE count
            prev_fte = 140  # Mock previous year FTE count
            fte_change = (
                ((current_fte - prev_fte) / prev_fte * 100) if prev_fte != 0 else 0
            )

            response_data = {
                "grossRevenue": {
                    "value": current_revenue,
                    "amount": round(revenue_change, 1),
                    "unit": "%",
                    "chartData": [
                        {
                            "name": str(year - 1),
                            "value": prev_revenue,
                            "color": "var(--color-green-400)",
                        },
                        {
                            "name": str(year),
                            "value": current_revenue,
                            "color": "var(--color-green-600)",
                        },
                    ],
                },
                "ebitda": {
                    "value": current_ebitda,
                    "amount": round(ebitda_change, 1),
                    "unit": "%",
                    "chartData": [
                        {
                            "name": str(year - 1),
                            "value": prev_ebitda,
                            "color": "var(--color-green-400)",
                        },
                        {
                            "name": str(year),
                            "value": current_ebitda,
                            "color": "var(--color-green-600)",
                        },
                    ],
                },
                "bankDebt": {
                    "value": current_debt,
                    "amount": round(debt_change, 1),
                    "unit": "%",
                    "chartData": [
                        {
                            "name": str(year - 1),
                            "value": prev_debt,
                            "color": "var(--color-red-400)",
                        },
                        {
                            "name": str(year),
                            "value": current_debt,
                            "color": "var(--color-red-600)",
                        },
                    ],
                    "additionalMetric": {
                        "value": (
                            f"{round((current_debt / current_revenue * 100), 1)}pts"
                            if current_revenue > 0
                            else "0pts"
                        ),
                        "label": "Debt Ratio",
                    },
                },
                "cashBalance": {
                    "value": current_cash,
                    "amount": round(cash_change, 1),
                    "unit": "%",
                    "chartData": [
                        {
                            "name": str(year - 1),
                            "value": prev_cash,
                            "color": "var(--color-blue-400)",
                        },
                        {
                            "name": str(year),
                            "value": current_cash,
                            "color": "var(--color-blue-600)",
                        },
                    ],
                },
                "fte": {
                    "value": current_fte,
                    "amount": round(fte_change, 1),
                    "unit": "%",
                    "chartData": [
                        {
                            "name": str(year - 1),
                            "value": prev_fte,
                            "color": "var(--color-purple-400)",
                        },
                        {
                            "name": str(year),
                            "value": current_fte,
                            "color": "var(--color-purple-600)",
                        },
                    ],
                },
            }

            return custom_success_response(response_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )
