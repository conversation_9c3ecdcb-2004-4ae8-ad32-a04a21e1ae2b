"use client"

import useSWRImmutable from "swr/immutable"

import { CustomResponse } from "@/types/response"
import {
  BusinessOverviewData,
  ProfitAndLossRevenueBreakdownData,
  ProfitAndLossRevenueData,
} from "@/types/summary"
import { createFetcher } from "@/services/fetcher"

export const useProfitAndLossRevenue = () => {
  const url =
    "/summary/profit-and-loss/revenue/chart/?currency=SGD&year=2025&last_n_years=4"

  const snapshot = useSWRImmutable<CustomResponse<ProfitAndLossRevenueData[]>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useProfitAndLossEBITDA = () => {
  const url =
    "/summary/profit-and-loss/ebitda/chart/?currency=SGD&year=2025&last_n_years=4"

  const snapshot = useSWRImmutable<CustomResponse<ProfitAndLossRevenueData[]>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useProfitAndLossNetProfit = () => {
  const url =
    "/summary/profit-and-loss/net-profit/chart/?currency=SGD&year=2025&last_n_years=4"

  const snapshot = useSWRImmutable<CustomResponse<ProfitAndLossRevenueData[]>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useProfitAndLossRevenueBreakdown = ({
  type,
}: {
  type: string
}) => {
  const url = `/summary/profit-and-loss/revenue-breakdown/${type}/chart/?currency=SGD&year=2025&last_n_years=4`

  const snapshot = useSWRImmutable<
    CustomResponse<ProfitAndLossRevenueBreakdownData[]>
  >(url, createFetcher)

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useBusinessOverview = () => {
  const url =
    "/summary/business-overview/?currency=SGD&year=2025&last_n_years=4"

  const snapshot = useSWRImmutable<CustomResponse<BusinessOverviewData>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data }
}
