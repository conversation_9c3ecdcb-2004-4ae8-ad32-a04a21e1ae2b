export interface ProfitAndLossRevenueData {
  name: string
  amount: number
  percentage: number
}

export type ProfitAndLossRevenueBreakdownData = StackedBarData & {
  total: number
}

export interface BankNetDebtData {
  name: string
  bankDebt: number
  netDebt: number
}

export interface OCFData {
  name: string
  amount: number
}

export interface WaterfallChartData {
  name: string
  amount: number
  remaining: number
}

export interface PatientsData {
  name: string
  barValue: number
  lineValue?: number
}

export type StackedBarData = {
  name: string
} & {
  [key: string]: number
}

export interface BusinessOverviewData {
  grossRevenue: BusinessMetric
  ebitda: BusinessMetric
  bankDebt: BusinessMetric
  cashBalance: BusinessMetric
  fte: BusinessMetric
}

export interface BusinessMetric {
  value: number
  amount: number
  unit: string
  chartData: BusinessMetricChartData[]
  additionalMetric?: {
    value: string
    label: string
  }
}

export interface BusinessMetricChartData {
  name: string
  value: number
  color?: string
}
