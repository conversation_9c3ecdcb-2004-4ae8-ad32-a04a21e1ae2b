"use client"

import React from "react"
import dynamic from "next/dynamic"
import { <PERSON>, Line, LineChart, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

import { BusinessMetricChartData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { cn } from "@/lib/utils"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { useBusinessOverview } from "@/services/summary"

export const CustomPieChart = dynamic(() => Promise.resolve(_CustomPieChart), {
  ssr: false,
})
export const CustomLineChart = dynamic(
  () => Promise.resolve(_CustomLineChart),
  {
    ssr: false,
  }
)

const BusinessOverview = () => {
  const { data, isLoading } = useBusinessOverview()

  if (isLoading) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <Card key={index} className="animate-pulse rounded-lg py-3 shadow-xs">
            <CardContent className="flex flex-1 flex-col gap-2 px-3">
              <div className="h-4 w-3/4 rounded bg-gray-200"></div>
              <div className="h-8 w-1/2 rounded bg-gray-200"></div>
              <div className="h-6 w-1/3 rounded bg-gray-200"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!data) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-6">
        <div className="col-span-full text-center text-gray-500">
          No data available
        </div>
      </div>
    )
  }

  return (
    <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-6">
      <OverviewCard
        title="Gross Revenue"
        value={formatAbbreviatedCurrency(data.grossRevenue.value)}
        amount={data.grossRevenue.amount}
        unit={data.grossRevenue.unit}
        topLeftItem={<CustomPieChart data={data.grossRevenue.chartData} />}
        bottomRightItem={<CustomLineChart data={data.grossRevenue.chartData} />}
      />

      <OverviewCard
        title="EBITDA"
        value={formatAbbreviatedCurrency(data.ebitda.value)}
        amount={data.ebitda.amount}
        unit={data.ebitda.unit}
        topLeftItem={<CustomPieChart data={data.ebitda.chartData} />}
        bottomRightItem={<CustomLineChart data={data.ebitda.chartData} />}
      />

      <OverviewCard
        title="Bank Debt"
        value={formatAbbreviatedCurrency(data.bankDebt.value)}
        amount={data.bankDebt.amount}
        unit={data.bankDebt.unit}
        topLeftItem={
          data.bankDebt.additionalMetric ? (
            <div className="mt-1 text-center">
              <p className="text-base font-bold text-nowrap text-red-600">
                {data.bankDebt.additionalMetric.value}
              </p>
              <p className="text-[10px] leading-none text-nowrap">
                {data.bankDebt.additionalMetric.label}
              </p>
            </div>
          ) : (
            <CustomPieChart data={data.bankDebt.chartData} />
          )
        }
        bottomRightItem={<CustomLineChart data={data.bankDebt.chartData} />}
      />

      <OverviewCard
        title="Cash Balance"
        value={formatAbbreviatedCurrency(data.cashBalance.value)}
        amount={data.cashBalance.amount}
        unit={data.cashBalance.unit}
        topLeftItem={
          data.cashBalance.additionalMetric ? (
            <div className="mt-1 text-center">
              <p className="text-base font-bold text-nowrap text-red-600">
                {data.cashBalance.additionalMetric.value}
              </p>
              <p className="text-[10px] leading-none text-nowrap">
                {data.cashBalance.additionalMetric.label}
              </p>
            </div>
          ) : (
            <CustomPieChart data={data.cashBalance.chartData} />
          )
        }
        bottomRightItem={<CustomLineChart data={data.cashBalance.chartData} />}
      />

      <OverviewCard
        title="FTE"
        value={data.fte.value.toString()}
        amount={data.fte.amount}
        unit={data.fte.unit}
        topLeftItem={
          data.fte.additionalMetric ? (
            <div className="mt-1 text-center">
              <p className="text-base font-bold text-nowrap text-red-600">
                {data.fte.additionalMetric.value}
              </p>
              <p className="text-[10px] leading-none text-nowrap">
                {data.fte.additionalMetric.label}
              </p>
            </div>
          ) : (
            <CustomPieChart data={data.fte.chartData} />
          )
        }
        bottomRightItem={<CustomLineChart data={data.fte.chartData} />}
      />
    </div>
  )
}

export default BusinessOverview

export const OverviewCard = ({
  title,
  value,
  amount,
  unit,
  topLeftItem,
  bottomRightItem,
}: {
  title: string
  value: string
  amount: number
  unit: string
  topLeftItem?: React.ReactNode
  bottomRightItem?: React.ReactNode
}) => (
  <Card className="rounded-lg py-3 shadow-xs">
    <CardContent className="flex flex-1 flex-col gap-2 px-3">
      <div className="flex h-4 items-center justify-between gap-2">
        <CardTitle className="text-muted-foreground text-sm leading-tight font-medium">
          {title}
        </CardTitle>

        {topLeftItem}
      </div>

      <p className="-mt-0.5 text-2xl font-bold">{value}</p>

      <div className="mt-auto flex items-end justify-between gap-2">
        <div
          className={cn(
            "rounded-full px-3 py-1.5 text-sm font-semibold",
            amount === 0 && "bg-muted text-muted-foreground",
            amount < 0 && "bg-red-100 text-red-600",
            amount > 0 && "bg-green-100 text-green-600"
          )}
        >
          {amount > 0 ? "+" : ""}
          {amount}
          {unit}
        </div>

        {bottomRightItem}
      </div>
    </CardContent>
  </Card>
)

const _CustomPieChart = ({ data }: { data?: BusinessMetricChartData[] }) => {
  const chartData = data || [
    { name: "2023", value: 100, color: "var(--color-green-400)" },
    { name: "2024", value: 100, color: "var(--color-green-600)" },
    { name: "2025", value: 100, color: "var(--color-green-800)" },
  ]

  // Calculate percentage for display (could be based on latest vs previous year)
  const percentage =
    data && data.length >= 2
      ? Math.round(
          ((data[data.length - 1].value - data[data.length - 2].value) /
            data[data.length - 2].value) *
            100
        )
      : 90

  return (
    <div className="relative mt-1 shrink-0">
      <PieChart width={48} height={32}>
        <Pie
          data={chartData}
          dataKey="value"
          cx="50%"
          cy="100%"
          startAngle={180}
          endAngle={0}
          innerRadius={18}
          outerRadius={24}
          paddingAngle={0}
        >
          {chartData.map((item, index) => (
            <Cell
              key={`cell-${index}`}
              fill={item.color || `var(--color-green-${400 + index * 200})`}
            />
          ))}
        </Pie>
      </PieChart>

      <div className="absolute inset-0 flex items-end justify-center pb-px">
        <span className="text-xs font-bold">{percentage}%</span>
      </div>
    </div>
  )
}

const _CustomLineChart = ({ data }: { data?: BusinessMetricChartData[] }) => {
  const chartData = data || [
    {
      name: "2022",
      value: 1,
    },
    {
      name: "2023",
      value: 1.5,
    },
    {
      name: "2024",
      value: 1.5,
    },
    {
      name: "2025",
      value: 2,
    },
  ]

  return (
    <div className="-mt-3.5 flex flex-col items-center">
      <LineChart data={chartData} width={40} height={30}>
        <Line
          dataKey="value"
          stroke="var(--color-green-600)"
          strokeWidth={2}
          dot={false}
        />
        <YAxis hide domain={["dataMin", "dataMax"]} />
      </LineChart>

      <p className="text-center text-xs">Last 3yrs</p>
    </div>
  )
}
